/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx,ts,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#0079C1',
          600: '#0066a3',
          700: '#005285',
          800: '#003d66',
          900: '#002947',
        },
        accent: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#E30613',
          600: '#c70510',
          700: '#a8040d',
          800: '#89030a',
          900: '#6a0208',
        }
      },
      fontFamily: {
        'sans': ['Open Sans', 'system-ui', 'Arial', 'sans-serif'],
        'heading': ['Poppins', 'sans-serif'],
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.6s ease-out forwards',
        'bounce-in': 'bounceIn 0.6s ease-out forwards',
      }
    },
  },
  plugins: [],
};
