
:root{
  --color-primary: #0079C1; /* blue */
  --color-accent: #E30613;  /* red */
  --color-dark: #2E343B;    /* dark gray */
  --color-light: #F8FAFB;
  --max-width: 1140px;
  --radius: 10px;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations and utilities */
@layer utilities {
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
*{box-sizing:border-box}
html,body,#root{height:100%;margin:0;font-family:'Open Sans',system-ui,Arial,sans-serif;color:var(--color-dark);background:#fff}
.container{max-width:var(--max-width);margin:0 auto;padding:18px}

a{color:var(--color-primary);text-decoration:none}
a:hover{text-decoration:underline}

/* Header */
.site-header{background:linear-gradient(90deg, rgba(255,255,255,1), rgba(255,255,255,1));position:sticky;top:0;z-index:40;border-bottom:1px solid #eee}
.header-inner{display:flex;align-items:center;justify-content:space-between;padding:12px 18px}
.brand img{height:56px}
.main-nav ul{display:flex;gap:18px;list-style:none;margin:0;padding:0;align-items:center}
.main-nav a{padding:6px 10px;border-radius:6px;font-weight:600;color:var(--color-dark)}
.main-nav a:hover{background:rgba(0,0,0,0.04)}

/* Buttons */
.btn{display:inline-block;padding:10px 16px;border-radius:8px;font-weight:700;border:0;cursor:pointer}
.btn-primary{background:var(--color-primary);color:#fff}
.btn-primary:hover{filter:brightness(0.95)}
.btn-accent{background:var(--color-accent);color:#fff}
.btn-ghost{background:transparent;border:1px solid #ddd;color:var(--color-dark)}

/* Hero */
.hero{display:flex;gap:24px;align-items:center;padding:48px 0;background:linear-gradient(180deg, rgba(240,248,255,0.7), rgba(255,255,255,1));border-bottom:1px solid #f0f4f8}
.hero-inner{display:flex;gap:32px;align-items:center;width:100%}
.hero-copy{flex:1}
.hero-copy h1{font-family:'Poppins',sans-serif;font-size:36px;margin:0 0 12px;color:var(--color-dark)}
.hero-copy p{margin:0 0 18px;color:#333;line-height:1.5}
.hero-actions{display:flex;gap:12px}
.hero-image img{max-width:520px;border-radius:12px;box-shadow:0 8px 30px rgba(0,0,0,0.08)}

/* Sections */
.section{padding:40px 0}
.section h2{font-family:'Poppins',sans-serif;color:var(--color-dark);margin-bottom:18px}

/* Cards */
.cards{display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:18px}
.card{background:#fff;border-radius:10px;padding:18px;box-shadow:0 6px 20px rgba(31,41,55,0.06);border:1px solid #f1f5f9}
.card h3{margin-top:0;color:var(--color-primary)}

/* Doctor card */
.doctor-card{text-align:center;padding:12px;border-radius:8px}
.doctor-card img{width:110px;height:110px;object-fit:cover;border-radius:999px;margin-bottom:8px}

/* CTA Banner */
.cta-banner{background:linear-gradient(90deg,var(--color-primary),var(--color-accent));color:#fff;padding:28px;border-radius:12px;display:flex;align-items:center;justify-content:space-between;margin:20px 0}

/* Footer */
.site-footer{background:var(--color-dark);color:#fff;padding:28px 18px;margin-top:40px}
.footer-inner{display:flex;gap:24px;max-width:var(--max-width);margin:0 auto;align-items:flex-start;justify-content:space-between}
.footer-brand img{height:48px;margin-bottom:6px}
.footer-links a{color:#fff;display:block;margin-bottom:6px}

/* WhatsApp floating button */
.whatsapp-fab{position:fixed;right:18px;bottom:18px;background:var(--color-accent);color:#fff;border-radius:999px;padding:14px 16px;box-shadow:0 6px 18px rgba(0,0,0,0.18);display:flex;gap:8px;align-items:center;z-index:60}
.whatsapp-fab img{height:20px;width:20px}

/* Responsive */
@media (max-width:880px){
  .hero-inner{flex-direction:column;text-align:center}
  .hero-image img{max-width:90%}
  .footer-inner{flex-direction:column;align-items:center;text-align:center}
  .main-nav ul{display:none}
}
