
import React from 'react'
import { Link } from 'react-router-dom'
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  HeartIcon,
  ShieldCheckIcon,
  ClockIcon
} from '@heroicons/react/24/solid'

export default function Footer(){
  const quickLinks = [
    { to: '/', label: 'Home' },
    { to: '/departments', label: 'Departments' },
    { to: '/news', label: 'News' },
    { to: '/about', label: 'About Us' },
    { to: '/contact', label: 'Contact' }
  ]

  const services = [
    'Physician Clinic',
    'Endoscopy & Colonoscopy',
    'Pediatrics',
    'Radiology',
    'Surgery',
    'Laboratory'
  ]

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-6 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
          {/* Hospital Info */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <img
                src="/theme-assets/images/logo-footer.png"
                alt="Jalaram Hospital"
                className="h-12 w-auto mb-4"
              />
              <h3 className="text-2xl font-bold text-white mb-4">
                Jalaram St. Christopher Specialized Hospital
              </h3>
            </div>

            <p className="text-gray-300 leading-relaxed mb-6 max-w-md">
              Providing compassionate, patient-centered care with modern facilities and experienced specialists. Your health and well-being are our top priorities.
            </p>

            {/* Contact Info */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
                  <PhoneIcon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Emergency Hotline</div>
                  <a href="tel:+254726100462" className="text-red-400 hover:text-red-300 transition-colors">
                    +254 726 100462
                  </a>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <EnvelopeIcon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Email Us</div>
                  <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                  <MapPinIcon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-semibold">Location</div>
                  <div className="text-gray-300">Nakuru, Kenya</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.to}>
                  <Link
                    to={link.to}
                    className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center gap-2 group"
                  >
                    <span className="w-1 h-1 bg-blue-500 rounded-full group-hover:w-2 transition-all duration-200"></span>
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-xl font-bold mb-6">Our Services</h4>
            <ul className="space-y-3">
              {services.map((service) => (
                <li key={service}>
                  <Link
                    to="/departments"
                    className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center gap-2 group"
                  >
                    <span className="w-1 h-1 bg-red-500 rounded-full group-hover:w-2 transition-all duration-200"></span>
                    {service}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Hospital Hours & Features */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                <ClockIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="font-semibold">Operating Hours</div>
                <div className="text-gray-300 text-sm">Mon - Sat: 8:00 - 17:00</div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center">
                <HeartIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="font-semibold">24/7 Emergency</div>
                <div className="text-gray-300 text-sm">Always available for emergencies</div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center">
                <ShieldCheckIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <div className="font-semibold">ISO Certified</div>
                <div className="text-gray-300 text-sm">Quality healthcare standards</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800 bg-gray-950">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-400 text-sm">
              © {new Date().getFullYear()} Jalaram St. Christopher Specialized Hospital. All rights reserved.
            </div>
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <Link to="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link>
              <Link to="/terms" className="hover:text-white transition-colors">Terms of Service</Link>
              <span>Made with ❤️ for better healthcare</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
