

import React from 'react'
import { ArrowRightIcon, HeartIcon, UserGroupIcon, ShieldCheckIcon } from '@heroicons/react/24/solid'
import { Link } from 'react-router-dom'

function Hero(){
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0 bg-blue-50 bg-opacity-30"></div>
      </div>

      <div className="relative container mx-auto px-6 py-20 lg:py-32">
        <div className="flex flex-col lg:flex-row items-center gap-16">
          {/* Hero Content */}
          <div className="flex-1 text-center lg:text-left space-y-8">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold">
              <HeartIcon className="w-4 h-4" />
              Trusted Healthcare Since 2010
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
              <span className="text-blue-600">Compassionate</span> care.
              <br />
              <span className="text-red-600">Modern</span> facilities.
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-red-600 bg-clip-text text-transparent">
                Trusted specialists.
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-2xl">
              Jalaram St. Christopher Specialized Hospital provides patient-centered care across a full range of medical and surgical services in Nakuru.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                to="/appointments"
                className="group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center gap-3"
              >
                Book Appointment
                <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>

              <Link
                to="/departments"
                className="group bg-white hover:bg-gray-50 text-blue-700 border-2 border-blue-200 hover:border-blue-300 px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center gap-3"
              >
                Our Services
                <UserGroupIcon className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </Link>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap justify-center lg:justify-start gap-8 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">15+</div>
                <div className="text-gray-600">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">10k+</div>
                <div className="text-gray-600">Patients Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">24/7</div>
                <div className="text-gray-600">Emergency Care</div>
              </div>
            </div>
          </div>

          {/* Hero Image */}
          <div className="flex-1 relative">
            <div className="relative">
              {/* Main Image */}
              <div className="relative z-10 bg-white p-4 rounded-3xl shadow-2xl">
                <img
                  src="/theme-assets/images/hero-hospital.jpg"
                  alt="Modern Hospital Facility"
                  className="w-full h-96 lg:h-[500px] object-cover rounded-2xl"
                />
              </div>

              {/* Floating Cards */}
              <div className="absolute -top-4 -left-4 bg-white p-4 rounded-2xl shadow-xl z-20 animate-bounce">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <ShieldCheckIcon className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900">Safe & Secure</div>
                    <div className="text-sm text-gray-600">ISO Certified</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-4 -right-4 bg-white p-4 rounded-2xl shadow-xl z-20 animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <HeartIcon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900">Expert Care</div>
                    <div className="text-sm text-gray-600">Specialized Team</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

function WhyChoose(){
  const items = [
    {
      id: 1,
      title: 'Experienced Specialists',
      desc: 'Consultant physicians, surgeons and allied specialists with years of expertise.',
      icon: UserGroupIcon,
      color: 'blue'
    },
    {
      id: 2,
      title: 'Modern Equipment',
      desc: 'Advanced diagnostic and treatment facilities including state-of-the-art endoscopy.',
      icon: ShieldCheckIcon,
      color: 'green'
    },
    {
      id: 3,
      title: 'Compassionate Care',
      desc: 'Patient-focused approach with clear communication and personalized treatment.',
      icon: HeartIcon,
      color: 'red'
    }
  ]

  const colorClasses = {
    blue: {
      bg: 'bg-blue-100',
      icon: 'text-blue-600',
      border: 'border-blue-200',
      hover: 'hover:border-blue-300'
    },
    green: {
      bg: 'bg-green-100',
      icon: 'text-green-600',
      border: 'border-green-200',
      hover: 'hover:border-green-300'
    },
    red: {
      bg: 'bg-red-100',
      icon: 'text-red-600',
      border: 'border-red-200',
      hover: 'hover:border-red-300'
    }
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Why Choose <span className="text-blue-600">Jalaram Hospital</span>?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We combine medical excellence with compassionate care to provide the best healthcare experience for our patients and their families.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {items.map((item) => {
            const IconComponent = item.icon
            const colors = colorClasses[item.color]

            return (
              <div
                key={item.id}
                className={`group bg-white p-8 rounded-2xl shadow-lg border-2 ${colors.border} ${colors.hover} hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300`}
              >
                {/* Icon */}
                <div className={`w-16 h-16 ${colors.bg} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className={`w-8 h-8 ${colors.icon}`} />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                  {item.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {item.desc}
                </p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

function Services(){
  const data = [
    {
      id: 1,
      title: 'Physician Clinic',
      desc: 'Comprehensive internal medicine consultations and chronic disease management with personalized care plans.',
      gradient: 'from-blue-500 to-blue-600'
    },
    {
      id: 2,
      title: 'Endoscopy & Colonoscopy',
      desc: 'Advanced diagnostic and therapeutic endoscopic services using state-of-the-art equipment.',
      gradient: 'from-green-500 to-green-600'
    },
    {
      id: 3,
      title: 'Pediatrics',
      desc: 'Specialized child health services and inpatient pediatric care with family-centered approach.',
      gradient: 'from-purple-500 to-purple-600'
    },
    {
      id: 4,
      title: 'Radiology',
      desc: 'Comprehensive imaging services including digital X-ray, ultrasound, and advanced diagnostics.',
      gradient: 'from-orange-500 to-orange-600'
    },
    {
      id: 5,
      title: 'Surgery',
      desc: 'Expert general and minor surgical procedures performed by experienced surgical teams.',
      gradient: 'from-red-500 to-red-600'
    },
    {
      id: 6,
      title: 'Laboratory',
      desc: 'Full-service onsite laboratory for rapid diagnostic testing and accurate results.',
      gradient: 'from-teal-500 to-teal-600'
    }
  ]

  return (
    <section className="py-20 bg-white" id="services">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our <span className="text-blue-600">Medical Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive healthcare services delivered by our team of experienced medical professionals using the latest technology and evidence-based practices.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {data.map((service) => (
            <article
              key={service.id}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 hover:border-gray-200 overflow-hidden transform hover:-translate-y-2 transition-all duration-300"
            >
              {/* Gradient Header */}
              <div className={`h-2 bg-gradient-to-r ${service.gradient}`}></div>

              {/* Content */}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                  {service.title}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-6">
                  {service.desc}
                </p>

                {/* CTA Button */}
                <Link
                  to="/appointments"
                  className={`inline-flex items-center gap-2 bg-gradient-to-r ${service.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300`}
                >
                  Book Appointment
                  <ArrowRightIcon className="w-4 h-4" />
                </Link>
              </div>
            </article>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <Link
            to="/departments"
            className="inline-flex items-center gap-3 bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
          >
            View All Departments
            <ArrowRightIcon className="w-5 h-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}

function Testimonials(){
  const testimonials = [
    {
      id: 1,
      text: 'Excellent care and prompt service — the staff were very supportive throughout my treatment. The facilities are modern and clean.',
      author: 'A. Mwangi',
      role: 'Patient',
      rating: 5
    },
    {
      id: 2,
      text: 'The endoscopy unit is well equipped and the procedure was smooth and comfortable. Dr. Patel explained everything clearly.',
      author: 'S. Wanjiru',
      role: 'Patient',
      rating: 5
    },
    {
      id: 3,
      text: 'Outstanding pediatric care for my daughter. The staff made her feel comfortable and the treatment was excellent.',
      author: 'M. Kimani',
      role: 'Parent',
      rating: 5
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            What Our <span className="text-blue-600">Patients Say</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our patients have to say about their experience at Jalaram Hospital.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 hover:border-blue-200 transform hover:-translate-y-2 transition-all duration-300"
            >
              {/* Stars */}
              <div className="flex gap-1 mb-6">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                  </svg>
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-gray-700 leading-relaxed mb-6 italic">
                "{testimonial.text}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {testimonial.author.charAt(0)}
                </div>
                <div>
                  <div className="font-bold text-gray-900">{testimonial.author}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Stats */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-8 bg-white px-8 py-6 rounded-2xl shadow-lg">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">4.9/5</div>
              <div className="text-gray-600">Patient Rating</div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">98%</div>
              <div className="text-gray-600">Satisfaction Rate</div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">500+</div>
              <div className="text-gray-600">Reviews</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

// CTA Section Component
function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 via-blue-700 to-red-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black bg-opacity-20"></div>

      <div className="relative container mx-auto px-6 text-center">
        <div className="max-w-4xl mx-auto">
          <h3 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Book Your <span className="text-yellow-300">Appointment</span>?
          </h3>
          <p className="text-xl text-blue-100 mb-10 leading-relaxed">
            Take the first step towards better health. Our team is ready to provide you with exceptional medical care. Quick and easy online requests — we will contact you to confirm.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link
              to="/appointments"
              className="group bg-white hover:bg-gray-100 text-blue-700 px-10 py-5 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-300 flex items-center gap-4"
            >
              <HeartIcon className="w-6 h-6 text-red-500" />
              Request Appointment
              <ArrowRightIcon className="w-6 h-6 group-hover:translate-x-2 transition-transform" />
            </Link>

            <div className="flex items-center gap-4 text-white">
              <div className="text-center">
                <div className="text-2xl font-bold">+254 726 100462</div>
                <div className="text-blue-200">Emergency Hotline</div>
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 flex flex-wrap justify-center items-center gap-8 text-blue-100">
            <div className="flex items-center gap-2">
              <ShieldCheckIcon className="w-5 h-5" />
              <span>ISO Certified</span>
            </div>
            <div className="flex items-center gap-2">
              <HeartIcon className="w-5 h-5" />
              <span>15+ Years Experience</span>
            </div>
            <div className="flex items-center gap-2">
              <UserGroupIcon className="w-5 h-5" />
              <span>Expert Medical Team</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default function Home(){
  return (
    <div className="overflow-hidden">
      <Hero />
      <WhyChoose />
      <Services />
      <CTASection />
      <Testimonials />
    </div>
  )
}
